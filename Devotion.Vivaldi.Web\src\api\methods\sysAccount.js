import { post, get } from '@/api/request'

const sysAccount = {
  //根据id获取信息
  get(params) {
    return get('sysAccount/get', params)
  },
  //新增
  add(params) {
    return post('sysAccount/add', params)
  },
  //修改
  update(params) {
    return post('sysAccount/update', params)
  },

  //保存
  save(params) {
    return post('sysAccount/save', params)
  },
  //修改是否启用
  updateIsEnabled(params) {
    return get('sysAccount/updateIsEnabled', params)
  },
  //修改配置权限
  updateIsConfig(params) {
    return get('sysAccount/updateIsConfig', params)
  },
  //发送短信
  sendCode(params) {
    return get('sysAccount/sendCode', params)
  },
  getDate() {
    return get('sysAccount/GetDate')
  },
  updatePassword(params) {
    return get('sysAccount/UpdatePassword', params, true)
  },
  //
  updateIsPush(params) {
    return get('sysAccount/UpdateIsPush', params)
  },


}
export default sysAccount
