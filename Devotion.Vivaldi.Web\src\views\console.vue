<template>
  <div class="console-container">
    <div class="console-content">
      <h1 class="console-title">欢迎使用 VIVALDI</h1>
      <p class="console-subtitle">这里是您的控制台，开始管理您的项目吧！</p>
    </div>
  </div>
</template>
<script setup>
import sysAccount from '@/api/methods/sysAccount'
import { message } from 'ant-design-vue'

const init = () => {
  sysAccount.getDate().then(res => {
    console.log(res)
  })
}
init()

</script>
<style scoped>
.console-container {
  position: relative;
  height: 80vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.console-content {
  text-align: center;
  color: #000;
}

.console-title {
  font-size: 36px;
  margin-bottom: 20px;
  animation: fadeInDown 1s ease-out;
}

.console-subtitle {
  font-size: 18px;
  margin-bottom: 30px;
  opacity: 0.8;
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
