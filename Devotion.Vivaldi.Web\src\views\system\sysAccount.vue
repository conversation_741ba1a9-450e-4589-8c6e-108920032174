<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="sysAccount"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-isEnable="{ record }">
      <a-switch
        v-model:checked="record.isEnable"
        checked-children="是"
        un-checked-children="否"
        @change="handleSwitchChange(record)"
      />
    </template>
    <template #custom-isPush="{ record }">
      <a-switch
        v-model:checked="record.isPush"
        checked-children="是"
        un-checked-children="否"
        @change="handleIsPushChange(record)"
      />
    </template>


    <template #custom-isConfig="{ record }">
      <a-switch
        v-if="record.roleId == 4"
        v-model:checked="record.isConfig"
        checked-children="是"
        un-checked-children="否"
        @change="handleConfigChange(record)"
      />
    </template>
  </form-table>
  <!-- 新增修改 -->
  <SysAccountEdit
    :open="editopen"
    @close="editopen = false"
    @updateData="refreshData"
    ref="editRef"
  >
  </SysAccountEdit>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import FormTable from '../../components/FormTable.vue'
import SysAccountEdit from '../../components/edit/SysAccountEdit.vue'
import sysAccount from '@/api/methods/sysAccount'
import sysRole from '@/api/methods/sysRole'
const formState = ref({
  name: { label: '账号名称', value: '', type: 'text' },
  mobile: { label: '手机号', value: '', type: 'text' },
  roleId: {
    label: '角色',
    value: null,
    type: 'select',
    data: [],
  },
  createTime: { label: '日期范围', value: '', type: 'time' },
})

const fetchRoleData = async () => {
  sysRole.getList().then(res => {
    formState.value.roleId.data = res.data.map(role => ({
      label: role.name,
      value: role.id,
    }))
  })
}
// 使用 onMounted 在组件加载时调用 fetchRoleData
onMounted(() => {
  fetchRoleData()
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '账号名称',
    dataIndex: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
  },
  {
    title: '角色',
    dataIndex: 'roleName',
  },
  {
    title: '经销商',
    dataIndex: 'dealerName',
  },
  {
    title: '配置权限',
    key: 'isConfig',
  },
  {
    title: '报警推送',
    key: 'isPush',
  },
  {
    title: '是否启用',
    key: 'isEnable',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

///编辑
const editopen = ref(false)
const editRef = ref(null)

const edit = record => {
  editRef.value.init(record.id)
  editopen.value = true
}
const refreshData = () => {
  childRef.value.tableLoad()
}
//修改是否启用
const handleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysAccount.updateIsEnabled(data).then(() => {
    childRef.value.tableLoad()
  })
}
const handleConfigChange = record => {
  var data = {
    id: record.id,
    isConfig: record.isConfig,
  }
  sysAccount.updateIsConfig(data).then(() => {
    childRef.value.tableLoad()
  })
}
const handleIsPushChange = record => {
  var data = {
    id: record.id,
    isPush: record.isPush,
  }
  sysAccount.updateIsPush(data).then(() => {
    childRef.value.tableLoad()
  })
}

</script>

<style></style>
