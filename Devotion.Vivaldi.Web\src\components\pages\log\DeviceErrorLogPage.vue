<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="LogDeviceError"
    :where="recordwhere"
    ref="childRef"
    :rowSelect="false"
  >
  </form-table>
  <!-- 编辑 -->
</template>
<script setup>
import { ref, defineProps, reactive } from 'vue'
import FormTable from '../../../components/FormTable.vue'
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const formState = ref({
  createTime: { label: '日期范围', value: '', type: 'time' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '项目',
    dataIndex: 'projectName',
  },
  {
    title: 'Uid',
    dataIndex: 'uid',
  },
  // {
  //   title: '类型',
  //   dataIndex: 'content',
  // },
  {
    title: '端口号',
    dataIndex: 'port',
  },
  {
    title: '故障代码',
    dataIndex: 'errorCode',
  },
  {
    title: '故障描述',
    dataIndex: 'errorMsg',
  },

  {
    title: '故障时间',
    dataIndex: 'time',
  },
])
const childRef = ref(null)

//修改是否启用
const sysDictItemhandleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysDictItem.updateIsEnabled(data).then(() => {
    refreshData()
  })
}
//

//编辑
const defaultformInfo = {
  id: 0,
  dictId: props.recordwhere.dictId,
  label: '',
  colorClass: 'default',
  value: 0,
  sort: 0,
  isEnable: true,
  remark: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editopen = ref(false)
const editTitle = ref('新增')
const edit = record => {
  console.log(formInfo.dictId)
  // 触发自定义事件，父组件会监听这个事件
  if (record.id) {
    editTitle.value = '修改'
    sysDictItem.get({ id: record.id }).then(res => {
      Object.assign(formInfo, res.data)
      console.log(formInfo.dictId)
      editopen.value = true
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
    editopen.value = true
  }
}

const refreshData = () => {
  childRef.value.tableLoad()
}
defineExpose({
  refreshData,
})
</script>
