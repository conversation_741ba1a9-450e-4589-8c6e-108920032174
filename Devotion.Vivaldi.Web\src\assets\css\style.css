body {
  margin: 0;
  min-height: 100vh;
  background-color: #f5f5f5;

  /* display: flex;
    place-items: center;
    min-width: 320px;
    min-height: 100vh; */
}
#app {
  /* max-width: 1280px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center; */
}
section {
  height: 100vh;
}
.el-aside {
  height: 100vh;
  padding: 15px 0 5px 15px;
}
.left-menu {
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  height: 99%;
  background-color: #ffffff;
}
.table-toolbar {
  margin-bottom: 10px;
}
.full-modal {
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(80vh);
    overflow-y: auto;
  }
  .ant-modal-body {
    flex: 1;
  }
}
.full-modal1 {
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(90vh);
    overflow-y: auto;
  }
  .ant-modal-body {
    flex: 1;
  }
}

.routerview-content {
  padding: 24px;
  background-color: white;
  border-radius: 3px;
}
.ant-card-head {
  background-color: #f5f5f5 !important;
}
:where(.css-dev-only-do-not-override-1p3hq3p).ant-card-bordered {
  border: 1px solid rgba(226, 226, 226, 0.9) !important;
}

/* .full-screen-spin {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
} */